import React, { useEffect, useRef, useCallback } from 'react';
import { animate, svg, stagger } from 'animejs';

export type AnimatedLogoTrigger = 'mount' | 'hover' | 'scroll' | 'both';
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}



const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = 'mount',
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Initialize paths to final drawn state (for hover-only triggers)
  const initializePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector('svg');
      if (!svgEl || !svg || !animate) return;
      const nodeList = svgEl.querySelectorAll('path, line, polyline');
      if (!nodeList.length) return;
      if (strokeColor) {
        nodeList.forEach((el) => {
          (el as SVGElement).setAttribute('stroke', strokeColor);
        });
      }
      nodeList.forEach((el) => {
        const d = svg.createDrawable(el);
        // Set to final drawn state without animation
        animate(d, {
          draw: '1 1',
          duration: 0,
          loop: false,
        });
      });
    } catch (e) {
      // always fail safe
    }
  }, [strokeColor]);

  // V4 API: Animate each path, line, polyline with svg.createDrawable + animate
  const animatePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector('svg');
      if (!svgEl || !animate || !svg || !stagger) return;
      const nodeList = svgEl.querySelectorAll('path, line, polyline');
      if (!nodeList.length) return;
      if (strokeColor) {
        nodeList.forEach((el) => {
          (el as SVGElement).setAttribute('stroke', strokeColor);
        });
      }
      console.log('[AnimatedLogo SVG order]', Array.from(nodeList).map((el, i) => {
        return {
          idx: i,
          tag: el.tagName,
          d: el.getAttribute('d'),
          points: el.getAttribute('points'),
          id: el.getAttribute('id'),
        };
      }));

      // Calculate timing for the new animation sequence
      const drawDuration = duration * 0.3; // 30% for drawing phase
      const pauseDuration = duration * 0.; // 20% for pause
      const undrawDuration = duration * 0.3; // 30% for undrawing phase
      const staggerDelay = 100;

      // Total time for all elements to finish drawing
      const totalDrawTime = drawDuration + (nodeList.length - 1) * staggerDelay;

      nodeList.forEach((el, idx) => {
        const d = svg.createDrawable(el);

        // Phase 1: Draw (0 0 -> 0 1)
        animate(d, {
          draw: ['0 0', '0 1'],
          easing: 'easeOutQuad',
          duration: drawDuration,
          delay: delay + idx * staggerDelay,
          loop: false,
        });

        // Phase 2: Pause (stay at 0 1)
        // No animation needed, just wait

        // Phase 3: Undraw (0 1 -> 1 1)
        const undrawStartTime = delay + totalDrawTime + pauseDuration + idx * staggerDelay;
        setTimeout(() => {
          if (d) {
            animate(d, {
              draw: ['0 1', '1 1'],
              easing: 'easeInQuad',
              duration: undrawDuration,
              loop: false,
            });
          }
        }, undrawStartTime);
      });
    } catch (e) {
      // always fail safe
    }
  }, [duration, delay, strokeColor]);

  // Mount trigger
  useEffect(() => {
    if (trigger === 'mount' || trigger === 'both') {
      animatePaths();
    } else if (trigger === 'hover') {
      // Initialize paths to final state for hover-only triggers
      initializePaths();
    }
  }, [animatePaths, initializePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === 'hover' || trigger === 'both') {
      const handleHover = () => animatePaths();
      const node = containerRef.current;
      if (node) {
        node.addEventListener('mouseenter', handleHover);
        return () => node.removeEventListener('mouseenter', handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger
  useEffect(() => {
    if (trigger === 'scroll') {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              animatePaths();
            }
          },
          { threshold: 0.5 }
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  if (!SVGComponent) return null;
  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent style={{ display: 'block', width: '100%', height: '100%' }} />
    </div>
  );
};

export default AnimatedLogo;

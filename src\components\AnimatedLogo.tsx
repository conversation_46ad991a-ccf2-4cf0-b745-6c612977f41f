import React, { useEffect, useRef, useCallback } from 'react';
import { animate, svg, stagger } from 'animejs';

export type AnimatedLogoTrigger = 'mount' | 'hover' | 'scroll' | 'both';
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}

const DRAW_VALS = ['0 0', '0 1', '1 1'];

const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = 'mount',
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // V4 API: Animate each path, line, polyline with svg.createDrawable + animate
  const animatePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector('svg');
      if (!svgEl || !animate || !svg || !stagger) return;
      const nodeList = svgEl.querySelectorAll('path, line, polyline');
      if (!nodeList.length) return;
      if (strokeColor) {
        nodeList.forEach((el) => {
          (el as SVGElement).setAttribute('stroke', strokeColor);
        });
      }
      console.log('[AnimatedLogo SVG order]', Array.from(nodeList).map((el, i) => {
        return {
          idx: i,
          tag: el.tagName,
          d: el.getAttribute('d'),
          points: el.getAttribute('points'),
          id: el.getAttribute('id'),
        };
      }));
      nodeList.forEach((el, idx) => {
        const d = svg.createDrawable(el);
        animate(d, {
          draw: DRAW_VALS,
          easing: 'inOutQuad',
          duration,
          delay: delay + idx * 100,
          loop: false,
        });
      });
    } catch (e) {
      // always fail safe
    }
  }, [duration, delay, strokeColor]);

  // Mount trigger
  useEffect(() => {
    if (trigger === 'mount' || trigger === 'both') {
      animatePaths();
    }
  }, [animatePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === 'hover' || trigger === 'both') {
      const handleHover = () => animatePaths();
      const node = containerRef.current;
      if (node) {
        node.addEventListener('mouseenter', handleHover);
        return () => node.removeEventListener('mouseenter', handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger
  useEffect(() => {
    if (trigger === 'scroll') {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              animatePaths();
            }
          },
          { threshold: 0.5 }
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  if (!SVGComponent) return null;
  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent style={{ display: 'block', width: '100%', height: '100%' }} />
    </div>
  );
};

export default AnimatedLogo;

import { useState, useEffect } from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Services from './components/Services';
import WhyChooseUs from './components/WhyChooseUs';
import Process from './components/Process';
import FAQ from './components/FAQ';
import CallToAction from './components/CallToAction';
import WhyNow from './components/WhyNow';
import SelfAssessment from './components/SelfAssessment';
import ContactForm from './components/ContactForm';
import Footer from './components/Footer';
import './index.css';
import Logo2Full from './components/LOGO Full Purple.svg?react';
import AnimatedLogo from './components/AnimatedLogo';
const LOADING_LOGO_COLOR = '#fff';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-900">
        <div className="text-center">
          <span className="flex justify-center">
            <Logo2Full style={{ height: '3.5rem', width: 'auto', display: 'block' }} />
          </span>
          <p className="mt-2 text-white">Loading your experience...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header />
      <main>
        <Hero />
        <Services />
        <WhyChooseUs />
        <Process />
        <FAQ />
        <CallToAction />
        <WhyNow />
        <SelfAssessment />
        <ContactForm />
      </main>
      <Footer />
    </div>
  );
}

export default App;
